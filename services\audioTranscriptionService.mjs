/**
 * Audio Transcription Service
 * Handles audio file download from WhatsApp and transcription using Whisper models
 */

import { pipeline } from '@xenova/transformers';
import axios from 'axios';
import { whatsappService } from './whatsappService.mjs';
import { config } from '../config/environment.mjs';
import wavefile from 'wavefile';

class AudioTranscriptionService {
  constructor() {
    this.transcriber = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the Whisper transcription pipeline
   * This is done lazily to avoid cold start delays
   */
  async initializeTranscriber() {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Initializing Whisper transcription pipeline...');

      // Use a smaller Whisper model for Lambda compatibility
      // whisper-tiny.en is the smallest English-only model (~39MB)
      // whisper-base.en is larger but more accurate (~74MB)
      this.transcriber = await pipeline(
        'automatic-speech-recognition',
        'Xenova/whisper-tiny.en',
        {
          // Configure for Lambda environment
          cache_dir: '/tmp/transformers_cache',
          local_files_only: false,
          revision: 'main'
        }
      );

      this.isInitialized = true;
      console.log('Whisper transcription pipeline initialized successfully');

    } catch (error) {
      console.error('Error initializing transcription pipeline:', error);
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      throw new Error(`Failed to initialize audio transcription service: ${error.message}`);
    }
  }

  /**
   * Downloads audio file from WhatsApp
   * @param {string} mediaId - WhatsApp media ID
   * @returns {Buffer} Audio file buffer
   */
  async downloadAudioFile(mediaId) {
    try {
      console.log(`Downloading audio file with media ID: ${mediaId}`);

      // Get media URL from WhatsApp
      const mediaUrl = await whatsappService.getMediaUrl(mediaId);
      console.log(`Media URL obtained: ${mediaUrl}`);

      // Download the audio file
      const response = await axios.get(mediaUrl, {
        headers: {
          'Authorization': `Bearer ${config.whatsapp.accessToken}`
        },
        responseType: 'arraybuffer',
        timeout: 30000 // 30 second timeout
      });

      console.log(`Audio file downloaded successfully, size: ${response.data.byteLength} bytes`);
      return Buffer.from(response.data);

    } catch (error) {
      console.error('Error downloading audio file:', error);
      console.error('Download error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        mediaId: mediaId
      });
      throw new Error(`Failed to download audio file from WhatsApp: ${error.message}`);
    }
  }

  /**
   * Transcribes audio buffer to text using Whisper
   * @param {Buffer} audioBuffer - Audio file buffer
   * @param {string} mimeType - MIME type of the audio file
   * @returns {string} Transcribed text
   */
  async transcribeAudio(audioBuffer, mimeType = 'audio/ogg') {
    try {
      // Extract base MIME type for logging and processing
      const baseMimeType = mimeType ? mimeType.split(';')[0].trim() : 'audio/ogg';
      console.log(`Starting audio transcription, buffer size: ${audioBuffer.length} bytes, MIME type: ${mimeType} (base: ${baseMimeType})`);

      // Check audio file size limits (Lambda has memory constraints)
      const maxSizeBytes = 10 * 1024 * 1024; // 10MB limit
      if (audioBuffer.length > maxSizeBytes) {
        throw new Error('Audio file too large. Please send a shorter voice message.');
      }

      // Ensure transcriber is initialized
      await this.initializeTranscriber();

      // Process audio buffer directly using wavefile (Node.js compatible approach)
      console.log('Processing audio buffer with wavefile...');

      try {
        // Create WaveFile instance from buffer
        const wav = new wavefile.WaveFile(audioBuffer);
        console.log(`Original audio format: ${wav.fmt.sampleRate}Hz, ${wav.fmt.bitsPerSample}-bit, ${wav.fmt.numChannels} channel(s)`);

        // Convert to format required by Whisper
        wav.toBitDepth('32f'); // Pipeline expects input as a Float32Array
        wav.toSampleRate(16000); // Whisper expects audio with a sampling rate of 16000

        let audioData = wav.getSamples();
        console.log(`Audio samples extracted, type: ${audioData.constructor.name}`);

        // Handle multi-channel audio (convert to mono)
        if (Array.isArray(audioData)) {
          if (audioData.length > 1) {
            console.log(`Converting from ${audioData.length} channels to mono`);
            const SCALING_FACTOR = Math.sqrt(2);

            // Merge channels (into first channel to save memory)
            for (let i = 0; i < audioData[0].length; ++i) {
              audioData[0][i] = SCALING_FACTOR * (audioData[0][i] + audioData[1][i]) / 2;
            }
          }

          // Select first channel
          audioData = audioData[0];
        }

        console.log(`Final audio data: length=${audioData.length}, type=${audioData.constructor.name}`);

        // Add timeout for transcription to prevent Lambda timeouts
        const transcriptionPromise = this.transcriber(audioData, {
          // Transcription options
          language: 'english', // Specify language for better accuracy
          task: 'transcribe',   // 'transcribe' or 'translate'
          return_timestamps: false, // We don't need timestamps for this use case
          chunk_length_s: 30,   // Process in 30-second chunks
          stride_length_s: 5    // 5-second stride between chunks
        });

        // Set a timeout for transcription (Lambda has execution time limits)
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Transcription timeout')), 45000); // 45 second timeout
        });

        console.log('Starting transcription with Whisper...');
        const result = await Promise.race([transcriptionPromise, timeoutPromise]);
        console.log('Transcription completed, result:', result);

        const transcribedText = result.text || result;
        console.log(`Audio transcription completed: "${transcribedText}"`);

        // Clean up the transcribed text
        const cleanedText = this.cleanTranscribedText(transcribedText);

        return cleanedText;

      } catch (audioProcessingError) {
        console.error('Error processing audio with wavefile:', audioProcessingError);
        throw new Error(`Audio processing failed: ${audioProcessingError.message}`);
      }

    } catch (error) {
      console.error('Error transcribing audio:', error);
      console.error('Transcription error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        bufferSize: audioBuffer?.length,
        mimeType: mimeType
      });

      if (error.message.includes('timeout')) {
        throw new Error('Audio transcription took too long. Please try a shorter voice message.');
      }

      if (error.message.includes('too large')) {
        throw error; // Re-throw size error as-is
      }

      // Include the original error message for better debugging
      throw new Error(`Failed to transcribe audio file: ${error.message}`);
    }
  }

  /**
   * Cleans up transcribed text by removing common artifacts
   * @param {string} text - Raw transcribed text
   * @returns {string} Cleaned text
   */
  cleanTranscribedText(text) {
    if (!text || typeof text !== 'string') {
      return '';
    }

    return text
      .trim()
      // Remove common Whisper artifacts
      .replace(/^\[.*?\]\s*/, '') // Remove timestamp markers like [00:00.000 --> 00:05.000]
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/^[.,\s]+|[.,\s]+$/g, '') // Remove leading/trailing punctuation and spaces
      .trim();
  }

  /**
   * Processes an audio message from WhatsApp
   * @param {Object} audioMessage - Audio message object from WhatsApp webhook
   * @returns {string} Transcribed text
   */
  async processAudioMessage(audioMessage) {
    try {
      const { id: mediaId, mime_type: mimeType } = audioMessage;

      console.log(`Processing audio message - Media ID: ${mediaId}, MIME type: ${mimeType}`);

      // Validate supported audio formats
      if (!this.isSupportedAudioFormat(mimeType)) {
        throw new Error(`Unsupported audio format: ${mimeType}`);
      }

      // Download audio file
      const audioBuffer = await this.downloadAudioFile(mediaId);

      // Transcribe audio to text
      const transcribedText = await this.transcribeAudio(audioBuffer, mimeType);

      if (!transcribedText || transcribedText.length === 0) {
        throw new Error('No speech detected in audio message');
      }

      console.log(`Audio message processed successfully: "${transcribedText}"`);
      return transcribedText;

    } catch (error) {
      console.error('Error processing audio message:', error);
      console.error('Processing error details:', {
        message: error.message,
        stack: error.stack,
        audioMessage: audioMessage
      });
      throw error;
    }
  }

  /**
   * Checks if the audio format is supported
   * @param {string} mimeType - MIME type of the audio file
   * @returns {boolean} True if supported
   */
  isSupportedAudioFormat(mimeType) {
    if (!mimeType || typeof mimeType !== 'string') {
      return false;
    }

    // Extract the base MIME type (remove codec parameters)
    const baseMimeType = mimeType.split(';')[0].trim().toLowerCase();

    const supportedFormats = [
      'audio/ogg',           // WhatsApp voice messages (OGG Opus)
      'audio/mpeg',          // MP3
      'audio/mp4',           // MP4 audio
      'audio/wav',           // WAV
      'audio/webm',          // WebM audio
      'audio/aac',           // AAC
      'audio/3gpp'           // 3GP
    ];

    return supportedFormats.includes(baseMimeType);
  }

  /**
   * Gets a user-friendly error message for transcription failures
   * @param {Error} error - The error that occurred
   * @returns {string} User-friendly error message
   */
  getErrorMessage(error) {
    const errorMessage = error.message.toLowerCase();

    if (errorMessage.includes('unsupported audio format')) {
      return "I'm sorry, I can't process this audio format. Please try sending your voice message again.";
    }

    if (errorMessage.includes('too large')) {
      return "Your voice message is too long. Please try sending a shorter message (under 1 minute).";
    }

    if (errorMessage.includes('timeout') || errorMessage.includes('took too long')) {
      return "Your voice message is taking too long to process. Please try sending a shorter message.";
    }

    if (errorMessage.includes('no speech detected')) {
      return "I couldn't detect any speech in your audio message. Could you please try recording again?";
    }

    if (errorMessage.includes('download')) {
      return "I'm having trouble accessing your audio message. Please try sending it again.";
    }

    if (errorMessage.includes('initialize')) {
      return "I'm currently having technical difficulties processing audio messages. Please try again in a moment or send a text message instead.";
    }

    // Generic error message
    return "I'm sorry, I couldn't process your audio message right now. Please try again or send a text message instead.";
  }
}

export const audioTranscriptionService = new AudioTranscriptionService();