/**
 * Simple test script for audio transcription service
 * Tests the initialization and basic functionality
 */

import { audioTranscriptionService } from './services/audioTranscriptionService.mjs';

async function testAudioServiceInitialization() {
  try {
    console.log('🧪 Testing audio transcription service initialization...');
    
    // Test initialization
    await audioTranscriptionService.initializeTranscriber();
    
    console.log('✅ Audio transcription service initialized successfully!');
    
  } catch (error) {
    console.error('❌ Audio transcription service initialization failed:', error.message);
    return false;
  }
  
  return true;
}

async function testSupportedFormats() {
  console.log('\n🧪 Testing supported audio formats...');
  
  const testFormats = [
    'audio/ogg',
    'audio/mpeg',
    'audio/mp4',
    'audio/wav',
    'audio/webm',
    'audio/aac',
    'audio/3gpp',
    'video/mp4', // Should not be supported
    'text/plain' // Should not be supported
  ];
  
  testFormats.forEach(format => {
    const isSupported = audioTranscriptionService.isSupportedAudioFormat(format);
    const status = isSupported ? '✅' : '❌';
    console.log(`${status} ${format}: ${isSupported ? 'Supported' : 'Not supported'}`);
  });
}

async function testErrorMessages() {
  console.log('\n🧪 Testing error message generation...');
  
  const testErrors = [
    new Error('Unsupported audio format: video/mp4'),
    new Error('No speech detected in audio message'),
    new Error('Failed to download audio file'),
    new Error('Failed to initialize transcription service'),
    new Error('Audio file too large'),
    new Error('Transcription timeout'),
    new Error('Some unknown error occurred')
  ];
  
  testErrors.forEach(error => {
    const userMessage = audioTranscriptionService.getErrorMessage(error);
    console.log(`Error: "${error.message}"`);
    console.log(`User message: "${userMessage}"`);
    console.log('---');
  });
}

async function testTextCleaning() {
  console.log('\n🧪 Testing text cleaning functionality...');
  
  const testTexts = [
    '[00:00.000 --> 00:05.000] Hello world',
    '  Hello   world  ',
    '.,  Hello world  .,',
    '',
    null,
    undefined,
    'Normal text without artifacts'
  ];
  
  testTexts.forEach(text => {
    const cleaned = audioTranscriptionService.cleanTranscribedText(text);
    console.log(`Original: "${text}"`);
    console.log(`Cleaned: "${cleaned}"`);
    console.log('---');
  });
}

// Run tests
async function runTests() {
  console.log('🚀 Starting audio transcription service tests...');
  console.log('Note: This will download the Whisper model (~39MB) on first run.\n');
  
  // Test initialization (this is the most important test)
  const initSuccess = await testAudioServiceInitialization();
  
  // Run other tests regardless of initialization success
  await testSupportedFormats();
  await testErrorMessages();
  await testTextCleaning();
  
  console.log('\n🎉 Tests completed!');
  
  if (initSuccess) {
    console.log('\n✅ Audio transcription service is ready for use!');
    console.log('The service can now process WhatsApp audio messages.');
  } else {
    console.log('\n❌ Audio transcription service failed to initialize.');
    console.log('Please check your internet connection and try again.');
    console.log('The Whisper model needs to be downloaded on first use.');
  }
}

runTests().catch(console.error);
