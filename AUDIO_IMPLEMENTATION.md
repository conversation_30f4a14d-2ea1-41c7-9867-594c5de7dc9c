# Audio Message Support Implementation

This document describes the implementation of audio message support for the Cravin Concierge WhatsApp webhook handlers.

## Overview

The implementation adds speech-to-text transcription capabilities to the existing WhatsApp integration, allowing users to send voice notes that are automatically transcribed and processed by the AI agent.

## Key Features

- **Offline Speech Recognition**: Uses `@xenova/transformers` with Whisper models for local transcription
- **AWS Lambda Compatible**: Optimized for serverless environment with proper timeouts and memory management
- **Multiple Audio Format Support**: Handles OGG Opus, MP3, MP4, WAV, WebM, AAC, and 3GP formats
- **Comprehensive Error Handling**: User-friendly error messages for various failure scenarios
- **Seamless AI Integration**: Transcribed text is processed by the existing conversational AI agent

## Implementation Details

### 1. Dependencies Added

```json
{
  "@xenova/transformers": "^2.17.2"
}
```

### 2. New Service: Audio Transcription Service

**File**: `services/audioTranscriptionService.mjs`

**Key Components**:
- **Whisper Model**: Uses `Xenova/whisper-tiny.en` (39MB) for English transcription
- **Audio Download**: Downloads audio files from WhatsApp Business API
- **Transcription Pipeline**: Converts audio to text with configurable options
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **Text Cleaning**: Removes common transcription artifacts

**Key Methods**:
- `initializeTranscriber()`: Lazy initialization of Whisper model
- `downloadAudioFile(mediaId)`: Downloads audio from WhatsApp
- `transcribeAudio(audioBuffer, mimeType)`: Transcribes audio to text
- `processAudioMessage(audioMessage)`: Main processing method
- `getErrorMessage(error)`: Returns user-friendly error messages

### 3. Modified WhatsApp Message Handler

**File**: `handlers/messageHandler.mjs`

**Changes**:
- Added support for `audio` message type alongside existing `text` messages
- Integrated audio transcription service
- Enhanced error handling for audio processing failures
- Maintained existing AI agent integration flow

**Message Processing Flow**:
1. Receive WhatsApp webhook with audio message
2. Download audio file using media ID
3. Transcribe audio to text using Whisper
4. Process transcribed text with existing AI agent
5. Send AI response back to user

### 4. Error Handling

**Supported Error Scenarios**:
- Unsupported audio formats
- Audio files too large (>10MB)
- Transcription timeouts (>45 seconds)
- No speech detected in audio
- Download failures
- Service initialization failures

**User-Friendly Messages**:
- "Your voice message is too long. Please try sending a shorter message (under 1 minute)."
- "I couldn't detect any speech in your audio message. Could you please try recording again?"
- "I'm having trouble accessing your audio message. Please try sending it again."

### 5. Performance Optimizations

**Lambda Compatibility**:
- Model caching in `/tmp/transformers_cache`
- Lazy initialization to avoid cold start delays
- Timeout protection (45 seconds for transcription)
- Memory usage optimization with file size limits

**Audio Processing**:
- Supports chunked processing (30-second chunks with 5-second stride)
- Automatic format detection
- Text cleaning and normalization

## Supported Audio Formats

- **OGG Opus** (WhatsApp voice messages)
- **MP3** (MPEG audio)
- **MP4** (MP4 audio)
- **WAV** (Waveform audio)
- **WebM** (WebM audio)
- **AAC** (Advanced Audio Coding)
- **3GP** (3GPP audio)

## Testing

**Test File**: `test-audio.mjs`

**Test Coverage**:
- Service initialization
- Supported format validation
- Error message generation
- Text cleaning functionality

**Test Results**: ✅ All tests pass, Whisper model successfully initialized

## Usage Example

When a user sends a voice note to the WhatsApp bot:

1. **User**: Sends voice message saying "I'm looking for Italian restaurants near me"
2. **System**: Downloads and transcribes audio to text
3. **AI Agent**: Processes "I'm looking for Italian restaurants near me"
4. **Response**: AI provides restaurant recommendations as usual

## Configuration

**Environment Variables** (existing):
- `WHATSAPP_ACCESS_TOKEN`: For downloading media files
- `WHATSAPP_PHONE_NUMBER_ID`: WhatsApp Business API configuration
- All existing AI agent configuration variables

**No Additional Configuration Required**: The audio transcription service works with existing environment setup.

## Deployment Considerations

### AWS Lambda

**Memory Requirements**:
- Minimum 512MB RAM recommended
- Model download (~39MB) on first cold start
- Subsequent invocations use cached model

**Timeout Settings**:
- Minimum 60 seconds timeout recommended
- Allows for model initialization and transcription

**Storage**:
- Uses `/tmp` directory for model caching
- Automatic cleanup after Lambda execution

### Package Size

**Total Addition**: ~47MB (transformers.js + dependencies)
**Model Size**: ~39MB (Whisper tiny English model)
**Lambda Deployment**: Within AWS Lambda limits

## Integration with Existing Features

**Maintains Full Compatibility**:
- Text messages continue to work as before
- All existing AI agent features preserved
- User management and name extraction still functional
- wa.me link handling continues to work
- CTA message functionality unchanged

**Enhanced User Experience**:
- Users can now send voice notes in addition to text
- Seamless switching between text and voice input
- Consistent AI responses regardless of input method

## Future Enhancements

**Potential Improvements**:
- Support for multilingual transcription
- Voice activity detection
- Speaker diarization for group conversations
- Audio quality enhancement preprocessing
- Larger Whisper models for better accuracy (base/small)

## Troubleshooting

**Common Issues**:
1. **Model Download Fails**: Check internet connectivity during first deployment
2. **Transcription Timeout**: Reduce audio length or increase Lambda timeout
3. **Memory Issues**: Increase Lambda memory allocation
4. **Format Not Supported**: Check MIME type in WhatsApp webhook payload

**Monitoring**:
- Check CloudWatch logs for transcription errors
- Monitor Lambda execution duration and memory usage
- Track audio processing success rates
